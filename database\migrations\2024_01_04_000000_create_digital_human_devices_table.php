<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('digital_human_devices', function (Blueprint $table) {
            $table->id()->comment('ID');
            $table->unsignedBigInteger('digital_user_id')->nullable()->comment('数智账号ID');
            $table->string('code', 100)->nullable()->comment('设备编码');
            $table->tinyInteger('status')->default(1)->comment('状态1正常2禁用');
            $table->integer('login_count')->nullable()->comment('登录次数');
            $table->timestamps();
            
            // 索引
            $table->index('digital_user_id');
            $table->index('code');
            $table->index('status');
        });
        
        // 添加表注释
        DB::statement("ALTER TABLE `digital_human_devices` COMMENT = '生涯翼站--数智画框设备表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('digital_human_devices');
    }
};