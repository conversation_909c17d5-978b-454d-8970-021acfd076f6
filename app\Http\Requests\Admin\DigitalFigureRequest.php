<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseRequest;
use App\Models\Admin\DigitalFigure;
use Illuminate\Validation\Rule;

class DigitalFigureRequest extends BaseRequest
{
    /**
     * 创建时的验证规则
     *
     * @return array
     */
    public function createRules(): array
    {
        return [
            'cover' => [
                'required',
                'string',
                'max:255',
                'url',
            ],
            'animation' => [
                'required',
                'string',
                'max:255',
                'url',
            ],
            'video' => [
                'required',
                'string',
                'max:255',
                'url',
            ],
            'voice_code' => [
                'required',
                'string',
                'max:50',
            ],
            'name' => [
                'required',
                'string',
                'max:50',
            ],
            'title' => [
                'nullable',
                'string',
                'max:50',
            ],
            'spell' => [
                'required',
                'array',
            ],
            'is_dark_background' => [
                'required',
                'boolean',
            ],
            'ai_code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('digital_figures', 'ai_code'),
            ],
            'series' => [
                'nullable',
                'string',
                'max:100',
            ],
            'subject' => [
                'nullable',
                'string',
                'max:100',
            ],
            'prompt' => [
                'nullable',
                'string',
            ],
            'type' => [
                'nullable',
                'integer',
                Rule::in([DigitalFigure::TYPE_STANDARD, DigitalFigure::TYPE_INTERACTIVE]),
            ],
        ];
    }

    /**
     * 更新时的验证规则
     *
     * @return array
     */
    public function updateRules(): array
    {
        $figureId = $this->route('id');
        
        return [
            'cover' => [
                'required',
                'string',
                'max:255',
                'url',
            ],
            'animation' => [
                'required',
                'string',
                'max:255',
                'url',
            ],
            'video' => [
                'required',
                'string',
                'max:255',
                'url',
            ],
            'voice_code' => [
                'required',
                'string',
                'max:50',
            ],
            'name' => [
                'required',
                'string',
                'max:50',
            ],
            'title' => [
                'nullable',
                'string',
                'max:50',
            ],
            'spell' => [
                'required',
                'array',
            ],
            'is_dark_background' => [
                'required',
                'boolean',
            ],
            'ai_code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('digital_figures', 'ai_code')->ignore($figureId),
            ],
            'series' => [
                'nullable',
                'string',
                'max:100',
            ],
            'subject' => [
                'nullable',
                'string',
                'max:100',
            ],
            'prompt' => [
                'nullable',
                'string',
            ],
            'type' => [
                'nullable',
                'integer',
                Rule::in([DigitalFigure::TYPE_STANDARD, DigitalFigure::TYPE_INTERACTIVE]),
            ],
        ];
    }

    /**
     * 获取验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return match ($this->getMethod()) {
            'POST' => $this->createRules(),
            'PUT', 'PATCH' => $this->updateRules(),
            default => [],
        };
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'cover.required' => '封面图URL不能为空',
            'cover.string' => '封面图URL必须是字符串',
            'cover.max' => '封面图URL长度不能超过255个字符',
            'cover.url' => '封面图URL格式不正确',
            'animation.required' => '动画URL不能为空',
            'animation.string' => '动画URL必须是字符串',
            'animation.max' => '动画URL长度不能超过255个字符',
            'animation.url' => '动画URL格式不正确',
            'video.required' => '视频URL不能为空',
            'video.string' => '视频URL必须是字符串',
            'video.max' => '视频URL长度不能超过255个字符',
            'video.url' => '视频URL格式不正确',
            'voice_code.required' => '语音代码不能为空',
            'voice_code.string' => '语音代码必须是字符串',
            'voice_code.max' => '语音代码长度不能超过50个字符',
            'name.required' => '人物名称不能为空',
            'name.string' => '人物名称必须是字符串',
            'name.max' => '人物名称长度不能超过50个字符',
            'title.string' => '称呼必须是字符串',
            'title.max' => '称呼长度不能超过50个字符',
            'spell.required' => '拼音拼写数组不能为空',
            'spell.array' => '拼音拼写必须是数组格式',
            'is_dark_background.required' => '是否深色背景不能为空',
            'is_dark_background.boolean' => '是否深色背景必须是布尔值',
            'ai_code.required' => 'AI标识码不能为空',
            'ai_code.string' => 'AI标识码必须是字符串',
            'ai_code.max' => 'AI标识码长度不能超过50个字符',
            'ai_code.unique' => 'AI标识码已存在',
            'series.string' => '人物所属系列必须是字符串',
            'series.max' => '人物所属系列长度不能超过100个字符',
            'subject.string' => '人物所属学科方向必须是字符串',
            'subject.max' => '人物所属学科方向长度不能超过100个字符',
            'prompt.string' => '互动对话提示词必须是字符串',
            'type.integer' => '人物类型必须是整数',
            'type.in' => '人物类型值无效',
        ];
    }

    /**
     * 获取验证字段的自定义属性名称
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'cover' => '封面图URL',
            'animation' => '动画URL',
            'video' => '视频URL',
            'voice_code' => '语音代码',
            'name' => '人物名称',
            'title' => '称呼',
            'spell' => '拼音拼写数组',
            'is_dark_background' => '是否深色背景',
            'ai_code' => 'AI标识码',
            'series' => '人物所属系列',
            'subject' => '人物所属学科方向',
            'prompt' => '互动对话提示词',
            'type' => '人物类型',
        ];
    }
}