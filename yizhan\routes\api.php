<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 翼站 API Routes
|--------------------------------------------------------------------------
|
| 这里是翼站子应用的 API 路由
| 这些路由会被自动加上 'api/yizhan' 前缀
| 例如：Route::get('/', ...) 实际访问路径为 /api/yizhan/
|
*/

// 翼站 API 基础信息
Route::get('/', function () {
    return response()->json([
        'app' => 'Yizhan',
        'version' => '1.0.0',
        'status' => 'active'
    ]);
});

// 翼站用户信息（需要认证）
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// 翼站 API 路由示例
// Route::apiResource('posts', App\Http\Controllers\Yizhan\Api\PostController::class);
// Route::apiResource('articles', App\Http\Controllers\Yizhan\Api\ArticleController::class);

// 翼站公开 API
// Route::prefix('public')->group(function () {
//     Route::get('/posts', [App\Http\Controllers\Yizhan\Api\PublicController::class, 'posts']);
//     Route::get('/categories', [App\Http\Controllers\Yizhan\Api\PublicController::class, 'categories']);
// });

// 翼站认证相关 API
// Route::prefix('auth')->group(function () {
//     Route::post('/login', [App\Http\Controllers\Yizhan\Api\AuthController::class, 'login']);
//     Route::post('/register', [App\Http\Controllers\Yizhan\Api\AuthController::class, 'register']);
//     Route::middleware('auth:sanctum')->post('/logout', [App\Http\Controllers\Yizhan\Api\AuthController::class, 'logout']);
// });
