<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 翼站 Web Routes
|--------------------------------------------------------------------------
|
| 这里是翼站子应用的 Web 路由
| 这些路由会被自动加上 'yizhan' 前缀
| 例如：Route::get('/', ...) 实际访问路径为 /yizhan/
|
*/

// 翼站首页
Route::get('/', function () {
    return view('yizhan.index');
})->name('index');

// 翼站其他路由示例
Route::get('/about', function () {
    return view('yizhan.about');
})->name('about');

// 翼站控制器路由示例
// Route::get('/posts', [App\Http\Controllers\Yizhan\PostController::class, 'index'])->name('posts.index');
// Route::resource('articles', App\Http\Controllers\Yizhan\ArticleController::class);

// 翼站认证路由（如果需要独立认证）
// Route::prefix('auth')->name('auth.')->group(function () {
//     Route::get('/login', [App\Http\Controllers\Yizhan\AuthController::class, 'showLogin'])->name('login');
//     Route::post('/login', [App\Http\Controllers\Yizhan\AuthController::class, 'login']);
//     Route::post('/logout', [App\Http\Controllers\Yizhan\AuthController::class, 'logout'])->name('logout');
// });
