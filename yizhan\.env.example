# 翼站子应用环境配置示例

# 翼站应用配置
YIZHAN_APP_NAME="翼站"
YIZHAN_APP_ENV=local
YIZHAN_APP_DEBUG=true
YIZHAN_APP_URL=http://localhost/yizhan

# 翼站数据库配置（如果需要独立数据库）
YIZHAN_DB_CONNECTION=mysql
YIZHAN_DB_HOST=127.0.0.1
YIZHAN_DB_PORT=3306
YIZHAN_DB_DATABASE=yizhan
YIZHAN_DB_USERNAME=root
YIZHAN_DB_PASSWORD=

# 翼站缓存配置
YIZHAN_CACHE_DRIVER=file
YIZHAN_CACHE_PREFIX=yizhan_

# 翼站文件存储配置
YIZHAN_FILESYSTEM_DISK=yizhan_local
YIZHAN_UPLOAD_PATH=uploads/yizhan

# 翼站第三方服务配置
YIZHAN_API_KEY=
YIZHAN_API_SECRET=

# 翼站邮件配置（如果需要独立邮件配置）
YIZHAN_MAIL_MAILER=smtp
YIZHAN_MAIL_HOST=
YIZHAN_MAIL_PORT=587
YIZHAN_MAIL_USERNAME=
YIZHAN_MAIL_PASSWORD=
YIZHAN_MAIL_FROM_ADDRESS=
YIZHAN_MAIL_FROM_NAME="${YIZHAN_APP_NAME}"
