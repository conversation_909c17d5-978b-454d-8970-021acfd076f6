<?php

namespace <PERSON><PERSON>han\Providers;

use Illuminate\Support\ServiceProvider;
use Dotenv\Dotenv;

class YizhanConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 加载翼站独立的环境配置
        $this->loadYizhanEnvironment();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * 加载翼站的环境配置
     */
    protected function loadYizhanEnvironment(): void
    {
        $yizhanEnvPath = base_path('yizhan/.env');
        
        if (file_exists($yizhanEnvPath)) {
            $dotenv = Dotenv::createImmutable(base_path('yizhan'));
            $dotenv->load();
            
            // 可以设置翼站特有的配置前缀
            $this->setYizhanConfig();
        }
    }

    /**
     * 设置翼站特有的配置
     */
    protected function setYizhanConfig(): void
    {
        // 示例：设置翼站特有的数据库连接
        if (env('YIZHAN_DB_CONNECTION')) {
            config([
                'database.connections.yizhan' => [
                    'driver' => env('YIZHAN_DB_CONNECTION', 'mysql'),
                    'host' => env('YIZHAN_DB_HOST', '127.0.0.1'),
                    'port' => env('YIZHAN_DB_PORT', '3306'),
                    'database' => env('YIZHAN_DB_DATABASE', 'yizhan'),
                    'username' => env('YIZHAN_DB_USERNAME', 'root'),
                    'password' => env('YIZHAN_DB_PASSWORD', ''),
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_unicode_ci',
                    'prefix' => '',
                    'strict' => true,
                    'engine' => null,
                ]
            ]);
        }

        // 示例：设置翼站特有的缓存配置
        if (env('YIZHAN_CACHE_DRIVER')) {
            config([
                'cache.stores.yizhan' => [
                    'driver' => env('YIZHAN_CACHE_DRIVER', 'file'),
                    'path' => storage_path('framework/cache/yizhan'),
                ]
            ]);
        }
    }
}
