<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Data Sync Routes
|--------------------------------------------------------------------------
|
| 数据同步模块路由
|
*/

// 数据同步控制器已下线，相关路由移除
// 测试接口（无需认证）
Route::get('datasync/test', function () {
    return response()->json([
        'status' => 'success',
        'code' => 200,
        'message' => '数据同步模块接口测试成功',
        'data' => [
            'timestamp' => now()->toDateTimeString(),
            'test_mode' => true,
            'available_endpoints' => [
                'POST /admin/datasync/sync-school' => '同步学校数据',
                'POST /admin/datasync/sync-student' => '同步学生数据',
                'POST /admin/datasync/sync-teacher' => '同步教师数据',
                'POST /admin/datasync/sync-admin' => '同步教务人员数据',
                'POST /admin/datasync/batch-sync' => '批量同步数据',
                'DELETE /admin/datasync/delete-sync-data' => '删除同步数据',
                'PUT /admin/datasync/update-sync-data' => '更新同步数据',
                'GET /admin/datasync/sync-status' => '获取同步状态',
                'GET /admin/datasync/field-mapping' => '获取字段映射配置',
                'POST /admin/datasync/test-field-mapping' => '测试字段映射',
                'GET /admin/datasync/config' => '获取数据同步配置'
            ]
        ]
    ]);
});

// 学校数据同步测试接口（无需认证）
Route::post('datasync/test-school-sync', function (Request $request) {
    try {
        $syncHelper = new \App\Helpers\DataSyncHelper();

        // 测试学校数据
        $testSchoolData = [
            'id' => $request->input('id', 999),
            'name' => $request->input('name', '测试学校'),
            'code' => $request->input('code', 'TEST001'),
            'type' => $request->input('type', 1),
            'level' => $request->input('level', 1),
            'phone' => $request->input('phone', '010-12345678'),
            'email' => $request->input('email', '<EMAIL>'),
            'address' => $request->input('address', '测试地址'),
            'status' => $request->input('status', 1),
        ];

        // 调用同步，传入请求数据
        $result = $syncHelper->syncSchool($testSchoolData, $request->all());

        return response()->json([
            'status' => 'success',
            'code' => 200,
            'message' => '学校数据同步测试完成',
            'data' => [
                'test_school_data' => $testSchoolData,
                'request_data' => $request->all(),
                'sync_result' => $result
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'code' => 500,
            'message' => '学校数据同步测试失败',
            'error' => $e->getMessage()
        ]);
    }
});
