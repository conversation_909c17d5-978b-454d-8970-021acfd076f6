<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('digital_users', function (Blueprint $table) {
            $table->id();
            $table->string('username', 100)->comment('用户名');
            $table->string('password', 100)->comment('密码');
            $table->string('name', 255)->nullable()->comment('学校机构名称');
            $table->string('api_token', 100)->nullable()->comment('API令牌');
            $table->timestamp('token_expires_at')->nullable()->comment('token过期时间');
            $table->timestamp('expire_at')->default('1970-01-01 00:00:01')->comment('过期时间');
            $table->json('character_config')->nullable()->comment('人物配置信息');
            $table->tinyInteger('status')->default(1)->comment('状态1-正常 0-禁用');
            $table->timestamp('last_login_at')->nullable()->comment('最后登录时间');
            $table->integer('login_count')->default(0)->comment('登录次数');
            $table->tinyInteger('device_type')->nullable()->comment('设备类型1数智画框标准版2数智画框互动版3数智人');
            $table->tinyInteger('device_num')->nullable()->comment('设备数量');
            $table->timestamps();
            
            // 索引
            $table->unique('username', 'idx_username');
            $table->index('status', 'idx_status');
            $table->index('device_type', 'idx_device_type');
            $table->index('expire_at', 'idx_expire_at');
            $table->index('api_token', 'idx_api_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('digital_users');
    }
};