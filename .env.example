APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# 翼站子应用配置
YIZHAN_APP_NAME="翼站"
YIZHAN_APP_DEBUG=true
YIZHAN_APP_URL="${APP_URL}/yizhan"

# 翼站数据库配置（如果需要独立数据库，取消注释并配置）
# YIZHAN_USE_SEPARATE_DB=false
# YIZHAN_DB_CONNECTION=mysql
# YIZHAN_DB_HOST=127.0.0.1
# YIZHAN_DB_PORT=3306
# YIZHAN_DB_DATABASE=yizhan
# YIZHAN_DB_USERNAME=root
# YIZHAN_DB_PASSWORD=

# 翼站表前缀（如果共享数据库）
YIZHAN_TABLE_PREFIX=yizhan_

# 翼站缓存配置
YIZHAN_CACHE_DRIVER=file
YIZHAN_CACHE_PREFIX=yizhan_

# 翼站文件存储配置
YIZHAN_FILESYSTEM_DISK=public
YIZHAN_UPLOAD_PATH=uploads/yizhan
YIZHAN_MAX_FILE_SIZE=10240

# 翼站第三方服务配置
YIZHAN_API_KEY=
YIZHAN_API_SECRET=

# 翼站功能开关
YIZHAN_ENABLE_COMMENTS=true
YIZHAN_ENABLE_NOTIFICATIONS=true
YIZHAN_ENABLE_SEARCH=true
YIZHAN_ENABLE_ANALYTICS=false
