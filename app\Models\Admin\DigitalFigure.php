<?php

namespace App\Models\Admin;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;

class DigitalFigure extends BaseModel
{
    /**
     * 数据表名
     *
     * @var string
     */
    protected $table = 'digital_figures';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'cover',
        'animation',
        'video',
        'voice_code',
        'name',
        'title',
        'spell',
        'is_dark_background',
        'ai_code',
        'series',
        'subject',
        'prompt',
        'type',
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'spell' => 'array',
        'is_dark_background' => 'boolean',
        'type' => 'integer',
    ];

    // 人物类型常量
    const TYPE_STANDARD = 1; // 标准版
    const TYPE_INTERACTIVE = 2; // 互动版

    /**
     * 获取人物类型文本
     *
     * @return string
     */
    public function getTypeTextAttribute(): string
    {
        return match ($this->type) {
            self::TYPE_STANDARD => '标准版',
            self::TYPE_INTERACTIVE => '互动版',
            default => '未知',
        };
    }

    /**
     * 获取人物类型选项
     *
     * @return array
     */
    public static function getTypeOptions(): array
    {
        return [
            self::TYPE_STANDARD => '标准版',
            self::TYPE_INTERACTIVE => '互动版',
        ];
    }

    /**
     * 查询作用域：按人物名称搜索
     *
     * @param Builder $query
     * @param string $name
     * @return Builder
     */
    public function scopeByName(Builder $query, string $name): Builder
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 查询作用域：按AI标识码搜索
     *
     * @param Builder $query
     * @param string $aiCode
     * @return Builder
     */
    public function scopeByAiCode(Builder $query, string $aiCode): Builder
    {
        return $query->where('ai_code', $aiCode);
    }

    /**
     * 查询作用域：按系列筛选
     *
     * @param Builder $query
     * @param string $series
     * @return Builder
     */
    public function scopeBySeries(Builder $query, string $series): Builder
    {
        return $query->where('series', $series);
    }

    /**
     * 查询作用域：按学科方向筛选
     *
     * @param Builder $query
     * @param string $subject
     * @return Builder
     */
    public function scopeBySubject(Builder $query, string $subject): Builder
    {
        return $query->where('subject', $subject);
    }

    /**
     * 查询作用域：按人物类型筛选
     *
     * @param Builder $query
     * @param int $type
     * @return Builder
     */
    public function scopeByType(Builder $query, int $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * 查询作用域：标准版人物
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeStandard(Builder $query): Builder
    {
        return $query->where('type', self::TYPE_STANDARD);
    }

    /**
     * 查询作用域：互动版人物
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeInteractive(Builder $query): Builder
    {
        return $query->where('type', self::TYPE_INTERACTIVE);
    }
}