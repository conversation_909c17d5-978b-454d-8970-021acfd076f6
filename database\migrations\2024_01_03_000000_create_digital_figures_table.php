<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('digital_figures', function (Blueprint $table) {
            $table->id();
            $table->string('cover', 255)->comment('封面图URL');
            $table->string('animation', 255)->comment('动画URL');
            $table->string('video', 255)->comment('视频URL');
            $table->string('voice_code', 50)->comment('语音代码');
            $table->string('name', 50)->comment('人物名称');
            $table->string('title', 50)->nullable()->comment('称呼');
            $table->json('spell')->comment('拼音拼写数组');
            $table->tinyInteger('is_dark_background')->default(0)->comment('是否深色背景(1是/0否)');
            $table->string('ai_code', 50)->comment('AI标识码');
            $table->string('series', 100)->nullable()->comment('人物所属系列');
            $table->string('subject', 100)->nullable()->comment('人物所属学科方向，没有则为空');
            $table->text('prompt')->nullable()->comment('互动对话提示词');
            $table->tinyInteger('type')->nullable()->comment('人物类型1标准版2互动版');
            $table->timestamps();
            
            // 索引
            $table->index('name', 'idx_name');
            $table->index('ai_code', 'idx_ai_code');
            $table->index('series', 'idx_series');
            $table->index('subject', 'idx_subject');
            $table->index('type', 'idx_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('digital_figures');
    }
};