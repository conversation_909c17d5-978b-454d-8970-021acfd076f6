<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 使用父模块的实际中间件组合 - 避免触发不必要的服务提供者
Route::group(['prefix' => 'yizhan', 'middleware' => ['auth.refresh', 'access_log', 'throttle:500']], function () {
    // 基础认证路由 - 只需要登录
    Route::get('user', 'YizhanController@getUserInfo')->name('yizhan.user');
    Route::get('organization', 'YizhanController@getOrganizationData')->name('yizhan.organization');
});

// 管理员路由 - 使用实际的中间件组合
Route::group(['prefix' => 'yizhan/admin', 'middleware' => ['auth.refresh', 'access_log', 'throttle:200']], function () {
    Route::get('status', 'YizhanController@getModuleStatus')->name('yizhan.admin.status');
    Route::get('dashboard', 'YizhanController@adminDashboard')->name('yizhan.admin.dashboard');
});

// 公共接口 - 使用实际的中间件组合
Route::group(['prefix' => 'yizhan/public', 'middleware' => ['auth.refresh', 'access_log', 'throttle:200']], function () {
    Route::get('status', function () {
        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'module' => 'Yizhan',
                'status' => 'active',
                'version' => '1.0.0'
            ]
        ]);
    })->name('yizhan.public.status');
});