<?php

namespace App\Http\Requests\Admin;

use App\Models\Admin\DigitalHumanDevice;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DigitalHumanDeviceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'digital_user_id' => 'nullable|integer|exists:digital_users,id',
            'code' => [
                'nullable',
                'string',
                'max:100',
            ],
            'status' => [
                'required',
                'integer',
                Rule::in([DigitalHumanDevice::STATUS_NORMAL, DigitalHumanDevice::STATUS_DISABLED])
            ],
            'login_count' => 'nullable|integer|min:0',
        ];

        // 创建时的特殊规则
        if ($this->isMethod('POST')) {
            $rules['code'][] = 'unique:digital_human_devices,code';
        }

        // 更新时的特殊规则
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $deviceId = $this->route('id') ?? $this->route('digital_human_device');
            if ($deviceId) {
                $rules['code'][] = 'unique:digital_human_devices,code,' . $deviceId;
            }
        }

        return $rules;
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'digital_user_id.integer' => '数智账号ID必须是整数',
            'digital_user_id.exists' => '数智账号不存在',
            'code.string' => '设备编码必须是字符串',
            'code.max' => '设备编码不能超过100个字符',
            'code.unique' => '设备编码已存在',
            'status.required' => '状态不能为空',
            'status.integer' => '状态必须是整数',
            'status.in' => '状态值无效',
            'login_count.integer' => '登录次数必须是整数',
            'login_count.min' => '登录次数不能小于0',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'digital_user_id' => '数智账号ID',
            'code' => '设备编码',
            'status' => '状态',
            'login_count' => '登录次数',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // 如果没有提供状态，默认设置为正常
        if (!$this->has('status')) {
            $this->merge([
                'status' => DigitalHumanDevice::STATUS_NORMAL
            ]);
        }

        // 如果没有提供登录次数，默认设置为0
        if (!$this->has('login_count')) {
            $this->merge([
                'login_count' => 0
            ]);
        }
    }
}