<?php

namespace App\Services\Admin;

use App\Models\Admin\DigitalHumanDevice;
use App\Services\BaseService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class DigitalHumanDeviceService extends BaseService
{
    /**
     * 模型实例
     *
     * @var DigitalHumanDevice
     */
    protected DigitalHumanDevice $model;

    /**
     * 构造函数
     *
     * @param DigitalHumanDevice $model
     */
    public function __construct(DigitalHumanDevice $model)
    {
        $this->model = $model;
    }

    /**
     * 获取数智设备列表
     *
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getList(array $params = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery()->with('digitalUser');

        // 搜索条件
        if (!empty($params['code'])) {
            $query->byCode($params['code']);
        }

        if (isset($params['status']) && $params['status'] !== '') {
            $query->byStatus($params['status']);
        }

        if (!empty($params['digital_user_id'])) {
            $query->byDigitalUserId($params['digital_user_id']);
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }

    /**
     * 创建数智设备
     *
     * @param array $data
     * @return DigitalHumanDevice
     */
    public function create(array $data): DigitalHumanDevice
    {
        return $this->model->create($data);
    }

    /**
     * 更新数智设备
     *
     * @param int $id
     * @param array $data
     * @return DigitalHumanDevice
     */
    public function update(int $id, array $data): DigitalHumanDevice
    {
        $device = $this->findById($id);
        $device->update($data);
        return $device->fresh(['digitalUser']);
    }

    /**
     * 根据ID查找数智设备
     *
     * @param int $id
     * @return DigitalHumanDevice
     */
    public function findById(int $id): DigitalHumanDevice
    {
        $device = $this->model->with('digitalUser')->find($id);
        if (!$device) {
            $this->throwBusinessException('数智设备不存在');
        }
        return $device;
    }

    /**
     * 删除数智设备
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $device = $this->findById($id);
        return $device->delete();
    }

    /**
     * 批量删除数智设备
     *
     * @param array $ids
     * @return int
     */
    public function batchDelete(array $ids): int
    {
        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * 更新设备状态
     *
     * @param int $id
     * @param int $status
     * @return DigitalHumanDevice
     */
    public function updateStatus(int $id, int $status): DigitalHumanDevice
    {
        $device = $this->findById($id);
        $device->update(['status' => $status]);
        return $device->fresh(['digitalUser']);
    }

    /**
     * 根据设备编码查找设备
     *
     * @param string $code
     * @return DigitalHumanDevice|null
     */
    public function findByCode(string $code): ?DigitalHumanDevice
    {
        return $this->model->with('digitalUser')->where('code', $code)->first();
    }

    /**
     * 根据数智用户ID获取设备列表
     *
     * @param int $digitalUserId
     * @return Collection
     */
    public function getDevicesByUserId(int $digitalUserId): Collection
    {
        return $this->model->byDigitalUserId($digitalUserId)->get();
    }

    /**
     * 获取正常状态的设备列表
     *
     * @return Collection
     */
    public function getNormalDevices(): Collection
    {
        return $this->model->normal()->with('digitalUser')->get();
    }

    /**
     * 增加设备登录次数
     *
     * @param int $id
     * @return DigitalHumanDevice
     */
    public function incrementLoginCount(int $id): DigitalHumanDevice
    {
        $device = $this->findById($id);
        $device->incrementLoginCount();
        return $device->fresh(['digitalUser']);
    }

    /**
     * 根据设备编码增加登录次数
     *
     * @param string $code
     * @return DigitalHumanDevice|null
     */
    public function incrementLoginCountByCode(string $code): ?DigitalHumanDevice
    {
        $device = $this->findByCode($code);
        if ($device && $device->isNormal()) {
            $device->incrementLoginCount();
            return $device->fresh(['digitalUser']);
        }
        return null;
    }

    /**
     * 获取统计信息
     *
     * @return array
     */
    public function getStatistics(): array
    {
        $total = $this->model->count();
        $normalCount = $this->model->normal()->count();
        $disabledCount = $this->model->disabled()->count();
        $totalLoginCount = $this->model->sum('login_count');
        
        // 按数智用户统计设备数量
        $userDeviceStats = $this->model
            ->selectRaw('digital_user_id, COUNT(*) as device_count')
            ->whereNotNull('digital_user_id')
            ->groupBy('digital_user_id')
            ->with('digitalUser:id,username,name')
            ->get()
            ->map(function ($item) {
                return [
                    'digital_user_id' => $item->digital_user_id,
                    'username' => $item->digitalUser->username ?? '',
                    'name' => $item->digitalUser->name ?? '',
                    'device_count' => $item->device_count,
                ];
            })
            ->toArray();
        
        return [
            'total' => $total,
            'normal_count' => $normalCount,
            'disabled_count' => $disabledCount,
            'total_login_count' => $totalLoginCount,
            'user_device_stats' => $userDeviceStats,
        ];
    }

    /**
     * 检查设备编码是否存在
     *
     * @param string $code
     * @param int|null $excludeId
     * @return bool
     */
    public function codeExists(string $code, ?int $excludeId = null): bool
    {
        $query = $this->model->where('code', $code);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * 断言设备编码唯一性
     *
     * @param string $code
     * @param int|null $excludeId
     * @return void
     */
    public function assertCodeUnique(string $code, ?int $excludeId = null): void
    {
        if ($this->codeExists($code, $excludeId)) {
            $this->throwBusinessException('设备编码已存在');
        }
    }
}