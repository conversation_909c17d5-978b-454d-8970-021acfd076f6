<?php

namespace App\Models\Admin;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DigitalHumanDevice extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'digital_human_devices';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'digital_user_id',
        'code',
        'status',
        'login_count',
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'digital_user_id' => 'integer',
        'status' => 'integer',
        'login_count' => 'integer',
    ];

    /**
     * 状态常量
     */
    const STATUS_NORMAL = 1;    // 正常
    const STATUS_DISABLED = 2;  // 禁用

    /**
     * 获取状态文本
     *
     * @param int $status
     * @return string
     */
    public static function getStatusText(int $status): string
    {
        $statusMap = [
            self::STATUS_NORMAL => '正常',
            self::STATUS_DISABLED => '禁用',
        ];

        return $statusMap[$status] ?? '未知';
    }

    /**
     * 获取状态选项
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_NORMAL => '正常',
            self::STATUS_DISABLED => '禁用',
        ];
    }

    /**
     * 获取状态文本属性
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatusText($this->status);
    }

    /**
     * 检查设备是否正常
     *
     * @return bool
     */
    public function isNormal(): bool
    {
        return $this->status === self::STATUS_NORMAL;
    }

    /**
     * 检查设备是否禁用
     *
     * @return bool
     */
    public function isDisabled(): bool
    {
        return $this->status === self::STATUS_DISABLED;
    }

    /**
     * 关联数智用户
     *
     * @return BelongsTo
     */
    public function digitalUser(): BelongsTo
    {
        return $this->belongsTo(DigitalUser::class, 'digital_user_id');
    }

    /**
     * 按状态筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, int $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 按设备编码筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $code
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('code', 'like', '%' . $code . '%');
    }

    /**
     * 按数智用户ID筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $digitalUserId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDigitalUserId($query, int $digitalUserId)
    {
        return $query->where('digital_user_id', $digitalUserId);
    }

    /**
     * 正常状态的设备
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNormal($query)
    {
        return $query->where('status', self::STATUS_NORMAL);
    }

    /**
     * 禁用状态的设备
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDisabled($query)
    {
        return $query->where('status', self::STATUS_DISABLED);
    }

    /**
     * 增加登录次数
     *
     * @return bool
     */
    public function incrementLoginCount(): bool
    {
        return $this->increment('login_count');
    }
}