<?php

namespace App\Models\Admin;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

class DigitalUser extends BaseModel
{
    /**
     * 数据表名
     *
     * @var string
     */
    protected $table = 'digital_users';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'username',
        'password',
        'name',
        'api_token',
        'token_expires_at',
        'expire_at',
        'character_config',
        'status',
        'last_login_at',
        'login_count',
        'device_type',
        'device_num',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'api_token',
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'character_config' => 'array',
        'status' => 'integer',
        'login_count' => 'integer',
        'device_type' => 'integer',
        'device_num' => 'integer',
        'token_expires_at' => 'datetime',
        'expire_at' => 'datetime',
        'last_login_at' => 'datetime',
    ];

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    // 设备类型常量
    const DEVICE_TYPE_STANDARD = 1; // 数智画框标准版
    const DEVICE_TYPE_INTERACTIVE = 2; // 数智画框互动版
    const DEVICE_TYPE_DIGITAL_HUMAN = 3; // 数智人

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_ENABLED => '正常',
            self::STATUS_DISABLED => '禁用',
            default => '未知',
        };
    }

    /**
     * 获取设备类型文本
     *
     * @return string
     */
    public function getDeviceTypeTextAttribute(): string
    {
        return match ($this->device_type) {
            self::DEVICE_TYPE_STANDARD => '数智画框标准版',
            self::DEVICE_TYPE_INTERACTIVE => '数智画框互动版',
            self::DEVICE_TYPE_DIGITAL_HUMAN => '数智人一体机',
            default => '未知',
        };
    }

    /**
     * 获取状态选项
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_ENABLED => '正常',
            self::STATUS_DISABLED => '禁用',
        ];
    }

    /**
     * 获取设备类型选项
     *
     * @return array
     */
    public static function getDeviceTypeOptions(): array
    {
        return [
            self::DEVICE_TYPE_STANDARD => '数智画框标准版',
            self::DEVICE_TYPE_INTERACTIVE => '数智画框互动版',
            self::DEVICE_TYPE_DIGITAL_HUMAN => '数智人一体机',
        ];
    }

    /**
     * 检查是否已过期
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->expire_at && $this->expire_at->isPast();
    }

    /**
     * 检查token是否已过期
     *
     * @return bool
     */
    public function isTokenExpired(): bool
    {
        return $this->token_expires_at && $this->token_expires_at->isPast();
    }

    /**
     * 查询作用域：启用状态
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 查询作用域：禁用状态
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeDisabled(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_DISABLED);
    }

    /**
     * 查询作用域：按用户名搜索
     *
     * @param Builder $query
     * @param string $username
     * @return Builder
     */
    public function scopeByUsername(Builder $query, string $username): Builder
    {
        return $query->where('username', 'like', "%{$username}%");
    }

    /**
     * 查询作用域：按机构名称搜索
     *
     * @param Builder $query
     * @param string $name
     * @return Builder
     */
    public function scopeByName(Builder $query, string $name): Builder
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 查询作用域：按设备类型筛选
     *
     * @param Builder $query
     * @param int $deviceType
     * @return Builder
     */
    public function scopeByDeviceType(Builder $query, int $deviceType): Builder
    {
        return $query->where('device_type', $deviceType);
    }

    /**
     * 查询作用域：未过期
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeNotExpired(Builder $query): Builder
    {
        return $query->where('expire_at', '>', now());
    }

    /**
     * 查询作用域：已过期
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expire_at', '<=', now());
    }
}