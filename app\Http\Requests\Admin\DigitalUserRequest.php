<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseRequest;
use App\Models\Admin\DigitalUser;
use Illuminate\Validation\Rule;

class DigitalUserRequest extends BaseRequest
{
    /**
     * 创建时的验证规则
     *
     * @return array
     */
    public function createRules(): array
    {
        return [
            'username' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z0-9_]+$/',
                Rule::unique('digital_users', 'username'),
            ],
            'password' => [
                'required',
                'string',
                'min:6',
                'max:100',
            ],
            'name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'expire_at' => [
                'required',
                'date',
                'after:now',
            ],
            'character_config' => [
                'nullable',
                'array',
            ],
            'status' => [
                'required',
                'integer',
                Rule::in([DigitalUser::STATUS_ENABLED, DigitalUser::STATUS_DISABLED]),
            ],
            'device_type' => [
                'nullable',
                'integer',
                Rule::in([
                    DigitalUser::DEVICE_TYPE_STANDARD,
                    DigitalUser::DEVICE_TYPE_INTERACTIVE,
                    DigitalUser::DEVICE_TYPE_DIGITAL_HUMAN,
                ]),
            ],
            'device_num' => [
                'nullable',
                'integer',
                'min:1',
                'max:255',
            ],
        ];
    }

    /**
     * 更新时的验证规则
     *
     * @return array
     */
    public function updateRules(): array
    {
        $userId = $this->route('id');
        
        return [
            'username' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z0-9_]+$/',
                Rule::unique('digital_users', 'username')->ignore($userId),
            ],
            'password' => [
                'nullable',
                'string',
                'min:6',
                'max:100',
            ],
            'name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'expire_at' => [
                'required',
                'date',
            ],
            'character_config' => [
                'nullable',
                'array',
            ],
            'status' => [
                'required',
                'integer',
                Rule::in([DigitalUser::STATUS_ENABLED, DigitalUser::STATUS_DISABLED]),
            ],
            'device_type' => [
                'nullable',
                'integer',
                Rule::in([
                    DigitalUser::DEVICE_TYPE_STANDARD,
                    DigitalUser::DEVICE_TYPE_INTERACTIVE,
                    DigitalUser::DEVICE_TYPE_DIGITAL_HUMAN,
                ]),
            ],
            'device_num' => [
                'nullable',
                'integer',
                'min:1',
                'max:255',
            ],
        ];
    }

    /**
     * 获取验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return match ($this->getMethod()) {
            'POST' => $this->createRules(),
            'PUT', 'PATCH' => $this->updateRules(),
            default => [],
        };
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'username.required' => '用户名不能为空',
            'username.string' => '用户名必须是字符串',
            'username.max' => '用户名长度不能超过100个字符',
            'username.regex' => '用户名只能包含字母、数字和下划线',
            'username.unique' => '用户名已存在',
            'password.required' => '密码不能为空',
            'password.string' => '密码必须是字符串',
            'password.min' => '密码长度不能少于6个字符',
            'password.max' => '密码长度不能超过100个字符',
            'name.string' => '机构名称必须是字符串',
            'name.max' => '机构名称长度不能超过255个字符',
            'expire_at.required' => '过期时间不能为空',
            'expire_at.date' => '过期时间格式不正确',
            'expire_at.after' => '过期时间必须大于当前时间',
            'character_config.array' => '人物配置必须是数组格式',
            'status.required' => '状态不能为空',
            'status.integer' => '状态必须是整数',
            'status.in' => '状态值无效',
            'device_type.integer' => '设备类型必须是整数',
            'device_type.in' => '设备类型值无效',
            'device_num.integer' => '设备数量必须是整数',
            'device_num.min' => '设备数量不能少于1',
            'device_num.max' => '设备数量不能超过255',
        ];
    }

    /**
     * 获取验证字段的自定义属性名称
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'username' => '用户名',
            'password' => '密码',
            'name' => '机构名称',
            'expire_at' => '过期时间',
            'character_config' => '人物配置',
            'status' => '状态',
            'device_type' => '设备类型',
            'device_num' => '设备数量',
        ];
    }
}