<?php

namespace App\Services\Admin;

use App\Models\Admin\DigitalUser;
use App\Services\BaseService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DigitalUserService extends BaseService
{
    /**
     * 模型实例
     *
     * @var DigitalUser
     */
    protected DigitalUser $model;

    /**
     * 构造函数
     *
     * @param DigitalUser $model
     */
    public function __construct(DigitalUser $model)
    {
        $this->model = $model;
    }

    /**
     * 获取数智设备用户列表
     *
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getList(array $params = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        // 搜索条件
        if (!empty($params['username'])) {
            $query->byUsername($params['username']);
        }

        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }

        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', $params['status']);
        }

        if (isset($params['device_type']) && $params['device_type'] !== '') {
            $query->byDeviceType($params['device_type']);
        }

        if (!empty($params['expired'])) {
            if ($params['expired'] === 'yes') {
                $query->expired();
            } elseif ($params['expired'] === 'no') {
                $query->notExpired();
            }
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }

    /**
     * 创建数智设备用户
     *
     * @param array $data
     * @return DigitalUser
     */
    public function create(array $data): DigitalUser
    {
        // 密码加密
        if (!empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        return $this->model->create($data);
    }

    /**
     * 更新数智设备用户
     *
     * @param int $id
     * @param array $data
     * @return DigitalUser
     */
    public function update(int $id, array $data): DigitalUser
    {
        $user = $this->findById($id);

        // 如果提供了新密码，则加密
        if (!empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            // 如果没有提供密码，则不更新密码字段
            unset($data['password']);
        }

        $user->update($data);
        return $user->fresh();
    }

    /**
     * 根据ID查找数智设备用户
     *
     * @param int $id
     * @return DigitalUser
     */
    public function findById(int $id): DigitalUser
    {
        $user = $this->model->find($id);
        if (!$user) {
            $this->throwBusinessException('数智设备用户不存在');
        }
        return $user;
    }

    /**
     * 删除数智设备用户
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $user = $this->findById($id);
        return $user->delete();
    }

    /**
     * 批量删除数智设备用户
     *
     * @param array $ids
     * @return int
     */
    public function batchDelete(array $ids): int
    {
        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * 更新用户状态
     *
     * @param int $id
     * @param int $status
     * @return DigitalUser
     */
    public function updateStatus(int $id, int $status): DigitalUser
    {
        $user = $this->findById($id);
        $user->update(['status' => $status]);
        return $user->fresh();
    }

    /**
     * 更新登录信息
     *
     * @param int $id
     * @return DigitalUser
     */
    public function updateLoginInfo(int $id): DigitalUser
    {
        $user = $this->findById($id);
        
        $user->update([
            'last_login_at' => now(),
            'login_count' => $user->login_count + 1,
        ]);
        
        return $user->fresh();
    }

    /**
     * 获取启用的用户列表
     *
     * @return Collection
     */
    public function getEnabledUsers(): Collection
    {
        return $this->model->enabled()->get();
    }

    /**
     * 检查用户名是否存在
     *
     * @param string $username
     * @param int|null $excludeId
     * @return bool
     */
    public function usernameExists(string $username, ?int $excludeId = null): bool
    {
        $query = $this->model->where('username', $username);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * 断言用户名唯一性
     *
     * @param string $username
     * @param int|null $excludeId
     * @return void
     */
    public function assertUsernameUnique(string $username, ?int $excludeId = null): void
    {
        if ($this->usernameExists($username, $excludeId)) {
            $this->throwBusinessException('用户名已存在');
        }
    }
}