<?php

namespace App\Services\Admin;

use App\Models\Admin\DigitalFigure;
use App\Services\BaseService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class DigitalFigureService extends BaseService
{
    /**
     * 模型实例
     *
     * @var DigitalFigure
     */
    protected DigitalFigure $model;

    /**
     * 构造函数
     *
     * @param DigitalFigure $model
     */
    public function __construct(DigitalFigure $model)
    {
        $this->model = $model;
    }

    /**
     * 获取数智画框人物列表
     *
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getList(array $params = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        // 搜索条件
        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }

        if (!empty($params['ai_code'])) {
            $query->byAiCode($params['ai_code']);
        }

        if (!empty($params['series'])) {
            $query->bySeries($params['series']);
        }

        if (!empty($params['subject'])) {
            $query->bySubject($params['subject']);
        }

        if (isset($params['type']) && $params['type'] !== '') {
            $query->byType($params['type']);
        }

        if (isset($params['is_dark_background']) && $params['is_dark_background'] !== '') {
            $query->where('is_dark_background', $params['is_dark_background']);
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }

    /**
     * 创建数智画框人物
     *
     * @param array $data
     * @return DigitalFigure
     */
    public function create(array $data): DigitalFigure
    {
        return $this->model->create($data);
    }

    /**
     * 更新数智画框人物
     *
     * @param int $id
     * @param array $data
     * @return DigitalFigure
     */
    public function update(int $id, array $data): DigitalFigure
    {
        $figure = $this->findById($id);
        $figure->update($data);
        return $figure->fresh();
    }

    /**
     * 根据ID查找数智画框人物
     *
     * @param int $id
     * @return DigitalFigure
     */
    public function findById(int $id): DigitalFigure
    {
        $figure = $this->model->find($id);
        if (!$figure) {
            $this->throwBusinessException('数智画框人物不存在');
        }
        return $figure;
    }

    /**
     * 删除数智画框人物
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $figure = $this->findById($id);
        return $figure->delete();
    }

    /**
     * 批量删除数智画框人物
     *
     * @param array $ids
     * @return int
     */
    public function batchDelete(array $ids): int
    {
        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * 根据AI标识码查找人物
     *
     * @param string $aiCode
     * @return DigitalFigure|null
     */
    public function findByAiCode(string $aiCode): ?DigitalFigure
    {
        return $this->model->byAiCode($aiCode)->first();
    }

    /**
     * 获取所有系列列表
     *
     * @return Collection
     */
    public function getSeriesList(): Collection
    {
        return $this->model
            ->select('series')
            ->whereNotNull('series')
            ->distinct()
            ->orderBy('series')
            ->pluck('series');
    }

    /**
     * 获取所有学科方向列表
     *
     * @return Collection
     */
    public function getSubjectList(): Collection
    {
        return $this->model
            ->select('subject')
            ->whereNotNull('subject')
            ->distinct()
            ->orderBy('subject')
            ->pluck('subject');
    }

    /**
     * 获取标准版人物列表
     *
     * @return Collection
     */
    public function getStandardFigures(): Collection
    {
        return $this->model->standard()->get();
    }

    /**
     * 获取互动版人物列表
     *
     * @return Collection
     */
    public function getInteractiveFigures(): Collection
    {
        return $this->model->interactive()->get();
    }

    /**
     * 获取统计信息
     *
     * @return array
     */
    public function getStatistics(): array
    {
        $total = $this->model->count();
        $standardCount = $this->model->standard()->count();
        $interactiveCount = $this->model->interactive()->count();
        $darkBackgroundCount = $this->model->where('is_dark_background', true)->count();
        
        // 按系列统计
        $seriesStats = $this->model
            ->selectRaw('series, COUNT(*) as count')
            ->whereNotNull('series')
            ->groupBy('series')
            ->pluck('count', 'series')
            ->toArray();
        
        // 按学科方向统计
        $subjectStats = $this->model
            ->selectRaw('subject, COUNT(*) as count')
            ->whereNotNull('subject')
            ->groupBy('subject')
            ->pluck('count', 'subject')
            ->toArray();
        
        return [
            'total' => $total,
            'standard_count' => $standardCount,
            'interactive_count' => $interactiveCount,
            'dark_background_count' => $darkBackgroundCount,
            'series_stats' => $seriesStats,
            'subject_stats' => $subjectStats,
        ];
    }

    /**
     * 检查AI标识码是否存在
     *
     * @param string $aiCode
     * @param int|null $excludeId
     * @return bool
     */
    public function aiCodeExists(string $aiCode, ?int $excludeId = null): bool
    {
        $query = $this->model->where('ai_code', $aiCode);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * 断言AI标识码唯一性
     *
     * @param string $aiCode
     * @param int|null $excludeId
     * @return void
     */
    public function assertAiCodeUnique(string $aiCode, ?int $excludeId = null): void
    {
        if ($this->aiCodeExists($aiCode, $excludeId)) {
            $this->throwBusinessException('AI标识码已存在');
        }
    }
}