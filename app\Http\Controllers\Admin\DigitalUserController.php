<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\DigitalUserRequest;
use App\Services\Admin\DigitalUserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DigitalUserController extends Controller
{
    /**
     * 数智设备用户服务
     *
     * @var DigitalUserService
     */
    protected DigitalUserService $digitalUserService;

    /**
     * 构造函数
     *
     * @param DigitalUserService $digitalUserService
     */
    public function __construct(DigitalUserService $digitalUserService)
    {
        $this->digitalUserService = $digitalUserService;
    }

    /**
     * 获取数智设备用户列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $params = $request->only([
            'username',
            'name',
            'status',
            'device_type',
            'expired',
            'sort_field',
            'sort_order',
            'per_page',
        ]);

        $users = $this->digitalUserService->getList($params);

        return $this->success($users, '获取数智设备用户列表成功');
    }

    /**
     * 创建数智设备用户
     *
     * @param DigitalUserRequest $request
     * @return JsonResponse
     */
    public function store(DigitalUserRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $this->digitalUserService->create($data);

        return $this->success($user, '创建数智设备用户成功');
    }

    /**
     * 获取数智设备用户详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $user = $this->digitalUserService->findById($id);

        return $this->success($user, '获取数智设备用户详情成功');
    }

    /**
     * 更新数智设备用户
     *
     * @param DigitalUserRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(DigitalUserRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $user = $this->digitalUserService->update($id, $data);

        return $this->success($user, '更新数智设备用户成功');
    }

    /**
     * 删除数智设备用户
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $this->digitalUserService->delete($id);

        return $this->success(null, '删除数智设备用户成功');
    }

    /**
     * 批量删除数智设备用户
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:digital_users,id',
        ]);

        $deletedCount = $this->digitalUserService->batchDelete($request->input('ids'));

        return $this->success(['deleted_count' => $deletedCount], '批量删除数智设备用户成功');
    }

    /**
     * 更新数智设备用户状态
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|integer|in:0,1',
        ]);

        $user = $this->digitalUserService->updateStatus($id, $request->input('status'));

        return $this->success($user, '更新数智设备用户状态成功');
    }

    /**
     * 检查用户名是否存在
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkUsername(Request $request): JsonResponse
    {
        $request->validate([
            'username' => 'required|string|max:100',
            'exclude_id' => 'nullable|integer',
        ]);

        $exists = $this->digitalUserService->usernameExists(
            $request->input('username'),
            $request->input('exclude_id')
        );

        return $this->success(['exists' => $exists], '检查用户名完成');
    }
}